"""
WebSocket流式对话处理
"""
import json
import re
from typing import <PERSON><PERSON>
from fastapi import APIRouter, WebSocket, WebSocketDisconnect

from app.services.task_service import get_or_create_task
from app.services.message_service import save_message
from app.services.llm_service import generate_llm_response
from app.services.report_service import update_report_content

websocket_router = APIRouter()

CODE_SNIPPET_HEAD = "<code_snippet>"
CODE_SNIPPET_TAIL = "</code_snippet>"


def check_if_possible_endwith_reserved_key(text: str, reserved_key: str) -> Tuple[bool, int]:
    for i in range(0, len(reserved_key)):
        if text.endswith(reserved_key[:i]):
            return True, len(text) - len(reserved_key[:i])

    return False, -1

def check_if_possible_startwith_reserved_key(text: str, reserved_key: str) -> Tuple[bool, int]:
    for i in range(0, len(reserved_key)):
        if text.startswith(reserved_key[i:]):
            return True, len(reserved_key) - i

    return False, -1

@websocket_router.websocket("/ws/chat/{task_id}")
async def websocket_chat(websocket: WebSocket, task_id: str):
    """WebSocket聊天端点"""
    await websocket.accept()
    
    try:
        # 确保任务存在
        task = await get_or_create_task(task_id)
        
        while True:
            # 接收用户消息
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error",
                    "content": "Invalid JSON format"
                })
                continue
            
            user_message = message_data.get("content", "")
            context_html = message_data.get("context_html", "")
            
            if not user_message.strip():
                continue
            
            # 保存用户消息
            await save_message(task_id, "user", user_message)
            
            # 生成LLM响应
            full_response = ""
            text_response = ""  # 只包含文本部分的响应
            in_code_snippet = False
            
            try:
                print(f"[DEBUG] Starting LLM response generation for task {task_id}")
                chunk_count = 0
                async for chunk in generate_llm_response(user_message, context_html):
                    chunk_count += 1
                    full_response += chunk
                    print(f"[DEBUG] Received chunk {chunk_count}, length: {len(chunk)}, in_code_snippet: {in_code_snippet}")
                    print(f"[DEBUG] Chunk content preview: {repr(chunk[:100])}")

                    # 当前生成内容不是代码内容
                    if not in_code_snippet:
                        # 并且当前内容末尾与<code_snippet>无关
                        flag, start_buffer_idx = check_if_possible_endwith_reserved_key(full_response, CODE_SNIPPET_HEAD)
                        if not flag:
                            await websocket.send_json({
                                "type": "assistant_message_chunk",
                                "content": chunk,
                                "is_code_snippet": False
                            })
                            text_response += chunk
                        else:
                            current_code_buffer = full_response[start_buffer_idx:]
                            in_code_snippet = True

                            chunk_text = chunk[: 1 - (len(full_response) - start_buffer_idx)]
                            if start_buffer_idx > 0:
                                await websocket.send_json({
                                    "type": "assistant_message_chunk",
                                    "content": chunk_text,
                                    "is_code_snippet": False
                                })
                            text_response += chunk_text

                    # 当前生成内容是代码内容
                    else:
                        # 如果代码标识头没有获取完全
                        if not current_code_buffer.startswith(CODE_SNIPPET_HEAD):
                            flag, _ = check_if_possible_startwith_reserved_key(chunk, CODE_SNIPPET_HEAD)
                            if not flag:
                                raise ValueError("Invalid code snippet format")
                            else:
                                await websocket.send_json({
                                "type": "code_snippet_start",
                                "content": "code snippet start",
                                "is_code_snippet": True
                            })
                                
                        current_code_buffer += chunk

                        # 如果current_code_buffer包含完整的标识尾
                        if CODE_SNIPPET_TAIL in current_code_buffer:
                            end_idx = current_code_buffer.find(CODE_SNIPPET_TAIL)
                            
                            current_text = current_code_buffer[end_idx + len(CODE_SNIPPET_TAIL):]
                            text_response += current_text

                            current_code_buffer = current_code_buffer[:end_idx + len(CODE_SNIPPET_TAIL)]

                            await websocket.send_json({
                                    "type": "code_snippet_end",
                                    "content": "code snippet end",
                                    "is_code_snippet": False
                                })
                            
                            if len(current_text) > 0:
                                await websocket.send_json({
                                    "type": "assistant_message_chunk",
                                    "content": current_text,
                                    "is_code_snippet": False
                                })

                            in_code_snippet = False

                print(f"[DEBUG] LLM response generation completed. Total chunks: {chunk_count}")
                print(f"[DEBUG] Text response length: {len(text_response)}, Full response length: {len(full_response)}")
                
            except Exception as e:
                await websocket.send_json({
                    "type": "error",
                    "content": f"LLM调用失败: {str(e)}"
                })

            # 保存助手的文本响应（无论是否有异常都要保存）
            try:
                message_content = text_response.strip() if text_response.strip() else "生成了报告内容"
                print(f"Saving assistant message for task {task_id}: {message_content[:50]}... )")

                await save_message(
                    task_id,
                    "assistant",
                    message_content,
                    len(current_code_buffer) > 0
                )

                if current_code_buffer:
                    await update_report_content(task_id, current_code_buffer.removeprefix(CODE_SNIPPET_HEAD).removesuffix(CODE_SNIPPET_TAIL))

                print(f"Assistant message saved successfully for task {task_id}")
            except Exception as e:
                print(f"Failed to save assistant message for task {task_id}: {e}")
    
    except WebSocketDisconnect:
        print(f"WebSocket disconnected for task {task_id}")
    except Exception as e:
        print(f"WebSocket error for task {task_id}: {str(e)}")
        try:
            await websocket.send_json({
                "type": "error",
                "content": f"服务器错误: {str(e)}"
            })
        except:
            pass
