"""
REST API路由
"""
from typing import List
from fastapi import APIRouter, HTTPException, Query
from pydantic import ValidationError

from app.models.schemas import (
    TaskSummary, TaskDetail, Config, ConfigUpdate,
    ReportContentRequest, ReportContentResponse,
    ErrorResponse
)
from app.services.task_service import get_tasks, get_task_detail, delete_task, delete_multiple_tasks
from app.services.config_service import get_config, update_config
from app.services.report_service import get_report_content, update_report_content, backup_report_content, list_report_backups


api_router = APIRouter()


@api_router.get("/tasks")
async def get_task_list(
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量")
):
    """获取任务列表"""
    try:
        tasks = await get_tasks(limit=limit, offset=offset)
        
        # 转换为字典格式
        tasks_data = [task.dict() for task in tasks]
        
        return {
            "success": True,
            "data": tasks_data
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get tasks",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}")
async def get_task_details(task_id: str):
    """获取任务详情"""
    try:
        task_detail = await get_task_detail(task_id)
        
        if not task_detail:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )
        
        return {
            "success": True,
            "data": {
                "task": task_detail.task.dict(),
                "messages": [msg.dict() for msg in task_detail.messages]
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get task detail",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.delete("/tasks/{task_id}")
async def delete_task_endpoint(task_id: str):
    """删除任务"""
    try:
        success = await delete_task(task_id)

        if not success:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )

        return {
            "success": True,
            "message": "Task deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to delete task",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.post("/tasks/batch-delete")
async def delete_multiple_tasks_endpoint(request: dict):
    """批量删除任务"""
    try:
        task_ids = request.get("task_ids", [])

        if not task_ids:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    error="No task IDs provided",
                    code="INVALID_REQUEST"
                ).dict()
            )

        deleted_count = await delete_multiple_tasks(task_ids)

        return {
            "success": True,
            "message": f"Successfully deleted {deleted_count} tasks",
            "deleted_count": deleted_count
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to delete tasks",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/report")
async def get_task_report_content(task_id: str):
    """获取任务报告内容"""
    try:
        report_content = await get_report_content(task_id)

        if not report_content:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Report not found",
                    code="REPORT_NOT_FOUND"
                ).dict()
            )

        return {
            "success": True,
            "data": report_content.dict()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get report content",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.put("/tasks/{task_id}/report")
async def update_task_report_content(task_id: str, request: ReportContentRequest):
    """更新任务报告内容"""
    try:
        # 先创建备份
        backup_path = await backup_report_content(task_id)

        # 更新报告内容
        updated_content = await update_report_content(task_id, request.content)

        if not updated_content:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found or failed to update report",
                    code="UPDATE_FAILED"
                ).dict()
            )

        return {
            "success": True,
            "data": updated_content.dict(),
            "backup_path": backup_path
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to update report content",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/report/backups")
async def get_task_report_backups(task_id: str):
    """获取任务报告备份列表"""
    try:
        backups = await list_report_backups(task_id)

        return {
            "success": True,
            "data": {
                "backups": backups,
                "count": len(backups)
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get report backups",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/config")
async def get_system_config():
    """获取系统配置"""
    try:
        config = await get_config()
        
        return {
            "success": True,
            "data": config.dict()
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get configuration",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.put("/config")
async def update_system_config(config_update: ConfigUpdate):
    """更新系统配置"""
    try:
        await update_config(config_update)
        
        return {
            "success": True,
            "message": "Configuration updated successfully"
        }
    
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=ErrorResponse(
                error="Invalid configuration",
                code="INVALID_CONFIG",
                details={"validation_errors": e.errors()}
            ).dict()
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to update configuration",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )
