



    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例报告</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/echarts@5.6.0/dist/echarts.min.js"></script>
    <!-- Prism.js 代码高亮 -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            color: #2d3748;
            line-height: 1.6;
        }
        .header-gradient {
            background: linear-gradient(135deg, #1e3a8a 0%, #0284c7 100%);
        }
        .toc-card {
            transition: transform 0.3s, box-shadow 0.3s;
            border-left: 4px solid #3b82f6;
        }
        .toc-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }
        .note-wrapper {
            position: relative;
            display: inline-block;
            border-bottom: 1px dashed #3b82f6;
            cursor: help;
        }
        .note-box {
            visibility: hidden;
            position: absolute;
            z-index: 10;
            width: 360px;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            color: #374151;
            border-radius: 6px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            opacity: 0;
            transition: opacity 0.3s, visibility 0.3s;
            border: 1px solid #e5e7eb;
            font-size: 0.9rem;
        }
        .note-wrapper:hover .note-box {
            visibility: visible;
            opacity: 1;
        }
        .note-box::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -10px;
            border-width: 10px;
            border-style: solid;
            border-color: white transparent transparent transparent;
        }
        .chart-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        a {
            color: #3b82f6;
            transition: color 0.2s;
        }
        a:hover {
            color: #1d4ed8;
        }
        .footnote-ref {
            vertical-align: super;
            font-size: 0.7em;
            color: #3b82f6;
            text-decoration: none;
            cursor: pointer;
        }
        .footnote-item {
            padding: 12px 16px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid #3b82f6;
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }
        .footnote-item:hover {
            background: #f1f5f9;
            transform: translateX(4px);
        }
        .footnote-item:last-child {
            margin-bottom: 0;
        }
        .chart-wrapper {
            width: 100%;
            height: clamp(200px, 50vw, 400px);
        }
        .block-container {
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            padding: 1.5rem 1rem;
            background: #fff;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            margin-top: 2.5rem;
            margin-bottom: 2.5rem;
        }
        .block-content {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow-x: auto;
        }
        .block-content img,
        .block-content table {
            width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }
        .block-caption {
            text-align: center;
            color: #64748b;
            margin-top: 0.75rem;
            font-size: 1rem;
        }
        .echart-box {
            width: 100%;
            aspect-ratio: 16 / 9; 
            min-height: 200px;
            max-height: 400px;
        }
        @media (max-width: 600px) {
            .echart-box {
                aspect-ratio: 4 / 3;
                min-height: 160px;
                max-height: 260px;
            }
        }
        .section-h1 {
            font-size: 1.875rem; /* text-3xl */
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .section-h2 {
            font-size: 1.5rem; /* text-2xl */
            font-weight: 700;
            margin-top: 2rem;
            margin-bottom: 1rem;
            border-bottom: 1px solid #bae6fd;
            padding-bottom: 0.25rem;
        }
        .section-h3 {
            font-size: 1.25rem; /* text-xl */
            font-weight: 700;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }
        .section-h4 {
            font-size: 1.125rem; /* text-lg */
            font-weight: 600;
            margin-top: 1.25rem;
            margin-bottom: 0.5rem;
        }
        .section-keywords {
            margin-bottom: 0.7rem;
            margin-top: 0.25rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5em;
        }
        .section-keyword-tag {
            display: inline-block;
            background: #e0f2fe;
            color: #2563eb;
            font-size: 0.92rem;
            border-radius: 0.5em;
            padding: 0.18em 0.9em;
            box-shadow: 0 1px 4px rgba(59,130,246,0.08);
            border: 1px solid #38bdf8;
            font-weight: 500;
            letter-spacing: 0.01em;
        }
        .section-divider {
            width: 100%;
            height: 0;
            border-bottom: 2px solid #bfdbfe;
            margin: 0.5rem 0 0.5rem 0;
        }
    </style>



    <!-- 报告封面 -->
    <div class="header-gradient min-h-screen flex flex-col items-center justify-center text-white py-20 px-4">
        <h1 class="text-5xl md:text-6xl font-bold mb-6 text-center">示例报告</h1>
        <p class="text-xl md:text-2xl font-light mb-8 text-center max-w-3xl leading-relaxed">这是一个只有标题=的示例报告</p>
        <p class="mt-16 text-blue-100">报告日期: 2024年12月19日</p>
    </div>

    <!-- 页脚 -->
    <footer class="bg-blue-800 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <p class="mb-1">Created by report agent</p>
            <p class="text-blue-200 text-sm">页面内容均由 AI 生成，仅供参考</p>
        </div>
    </footer>



