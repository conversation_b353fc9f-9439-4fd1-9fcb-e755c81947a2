# ReportViewer API 集成更改

## 概述

本文档记录了将 ReportViewer 组件从使用模拟数据改为调用真实 API 获取文档信息的更改。

## 主要更改

### 1. 移除模拟数据生成函数

- 删除了 `generateMockReportContent` 函数
- 该函数之前用于生成基于任务ID和标题的模拟HTML内容

### 2. 新增 API 调用函数

添加了 `loadReportContent` 函数，用于通过 API 获取真实的报告内容：

```typescript
const loadReportContent = useCallback(async (taskId: string) => {
  try {
    console.log('[DEBUG] ReportViewer: Loading report content for task:', taskId)
    
    const response = await reportService.getReportContent(taskId)
    
    if (response.success && response.data) {
      console.log('[DEBUG] ReportViewer: Successfully loaded report content')
      const content = response.data.content
      setReportContent(content)
      setEditableContent(content)
      setOriginalContent(content)
      
      // 同时更新store中的内容
      updateReport(content)
    } else {
      console.log('[DEBUG] ReportViewer: No report content found for task:', taskId)
      // 如果没有报告内容，清空显示
      setReportContent('')
      setEditableContent('')
      setOriginalContent('')
      updateReport('')
    }
  } catch (error) {
    console.error('[DEBUG] ReportViewer: Failed to load report content:', error)
    // 发生错误时也清空显示
    setReportContent('')
    setEditableContent('')
    setOriginalContent('')
    updateReport('')
  }
}, [updateReport])
```

### 3. 更新任务切换逻辑

修改了任务切换时的 useEffect，现在调用 API 而不是生成模拟数据：

```typescript
useEffect(() => {
  setSelectedText('')
  window.getSelection()?.removeAllRanges()

  // 退出编辑模式
  setIsEditing(false)
  setHasUnsavedChanges(false)

  // 根据当前任务加载报告内容
  if (currentTask?.id) {
    loadReportContent(currentTask.id)
  } else {
    setReportContent('')
    setEditableContent('')
    setOriginalContent('')
    updateReport('')
  }
}, [currentTask?.id, loadReportContent])
```

### 4. 更新报告路径加载函数

简化了 `loadReportFromPath` 函数，现在使用统一的 API 调用：

```typescript
const loadReportFromPath = useCallback(async (reportPath: string) => {
  try {
    console.log('[DEBUG] ReportViewer: Starting to load report from path:', reportPath)

    // 从报告路径提取任务ID
    const pathParts = reportPath.split('/')
    const taskId = pathParts[pathParts.length - 2] // 倒数第二个部分是任务ID

    console.log('[DEBUG] ReportViewer: Extracted taskId:', taskId)

    // 使用API加载报告内容
    await loadReportContent(taskId)
    console.log('[DEBUG] ReportViewer: Report loaded and updated successfully via API')
  } catch (error) {
    console.error('[DEBUG] ReportViewer: Error loading report:', error)
  }
}, [loadReportContent])
```

## API 接口

使用的 API 接口：

- `reportService.getReportContent(taskId: string)`: 获取指定任务的报告内容
- 返回格式：
  ```typescript
  {
    success: boolean,
    data: {
      content: string,
      file_path: string,
      last_modified: string
    }
  }
  ```

## 错误处理

- API 调用失败时，会清空显示内容并记录错误日志
- 没有找到报告内容时，会清空显示内容
- 所有错误都会在控制台输出调试信息

## 测试

添加了完整的单元测试，覆盖以下场景：

1. 成功加载报告内容
2. API 错误处理
3. 空响应处理
4. 无任务选择时的行为
5. 生成状态显示
6. 内容显示

测试文件位置：`frontend/src/components/report/ReportViewer/ReportViewer.test.tsx`

## 配置更改

为了支持测试，还进行了以下配置更改：

1. 更新了 `vite.config.ts` 以支持 Vitest
2. 添加了测试设置文件 `frontend/src/test/setup.ts`
3. 在 `package.json` 中添加了测试脚本

## 向后兼容性

这些更改保持了组件的外部接口不变，只是内部实现从模拟数据改为真实 API 调用。现有的功能如编辑、保存、导出等都保持不变。
