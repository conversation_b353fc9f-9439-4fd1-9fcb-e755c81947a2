/**
 * 报告内容管理服务
 */
import { APIClient } from './client'
import type { APIResponse } from '../../types/api'

export interface ReportContent {
  content: string
  file_path: string
  last_modified: string
}

export interface ReportContentRequest {
  content: string
}

export interface ReportBackup {
  backups: string[]
  count: number
}

class ReportService extends APIClient {
  /**
   * 获取报告内容
   */
  async getReportContent(taskId: string): Promise<APIResponse<ReportContent>> {
    return this.get<APIResponse<ReportContent>>(`/api/tasks/${taskId}/report`)
  }

  /**
   * 更新报告内容
   */
  async updateReportContent(taskId: string, content: string): Promise<APIResponse<ReportContent & { backup_path?: string }>> {
    return this.put<APIResponse<ReportContent & { backup_path?: string }>>(
      `/api/tasks/${taskId}/report`,
      { content }
    )
  }

  /**
   * 获取报告备份列表
   */
  async getReportBackups(taskId: string): Promise<APIResponse<ReportBackup>> {
    return this.get<APIResponse<ReportBackup>>(`/api/tasks/${taskId}/report/backups`)
  }

  /**
   * 从URL加载报告内容（用于现有的静态文件访问）
   */
  async loadReportFromUrl(reportUrl: string): Promise<string> {
    try {
      const response = await fetch(reportUrl)
      if (!response.ok) {
        throw new Error(`Failed to load report: ${response.status} ${response.statusText}`)
      }
      return await response.text()
    } catch (error) {
      console.error('[ReportService] Failed to load report from URL:', error)
      throw error
    }
  }

  /**
   * 导出报告为PDF（使用浏览器打印功能）
   */
  async exportToPDF(html: string, title: string = 'Report'): Promise<void> {
    try {
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>${title}</title>
              <style>
                body { 
                  margin: 0; 
                  padding: 20px; 
                  font-family: Arial, sans-serif; 
                  line-height: 1.6;
                }
                @media print { 
                  body { margin: 0; padding: 10px; } 
                  .no-print { display: none; }
                }
                h1, h2, h3 { color: #333; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
              </style>
            </head>
            <body>
              ${html}
            </body>
          </html>
        `)
        printWindow.document.close()
        
        // 等待内容加载完成后打印
        printWindow.onload = () => {
          printWindow.print()
          printWindow.close()
        }
      }
    } catch (error) {
      console.error('[ReportService] Failed to export PDF:', error)
      throw error
    }
  }
}

export const reportService = new ReportService()
