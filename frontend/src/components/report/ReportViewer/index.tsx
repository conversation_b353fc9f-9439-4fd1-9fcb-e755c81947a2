import React, { useState, useEffect, useCallback } from 'react'
import clsx from 'clsx'
import { useTaskStore, useReportStore } from '../../../stores'
import { reportService } from '../../../services/api/reportService'
import EditableContent from '../EditableContent'
import EditToolbar from '../EditToolbar'

interface ReportViewerProps {
  className?: string
}

const ReportViewer: React.FC<ReportViewerProps> = ({
  className = ''
}) => {
  const { currentTask } = useTaskStore()
  const { reportHtml, isGenerating, updateReport, startGeneration, finishGeneration } = useReportStore()

  // 编辑相关状态
  const [isEditing, setIsEditing] = useState(false)
  const [editableContent, setEditableContent] = useState('')
  const [originalContent, setOriginalContent] = useState('')
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // 原有状态
  const [selectedText, setSelectedText] = useState('')
  const [reportContent, setReportContent] = useState('')

  // 监听报告生成事件
  useEffect(() => {
    const handleReportGenerationStart = () => {
      console.log('[DEBUG] ReportViewer: Report generation started')
      startGeneration()
      setReportContent('')
      // 清空之前的报告内容
      updateReport('')
      console.log('[DEBUG] ReportViewer: Cleared previous report content')
    }

    const handleReportGenerationEnd = (event: CustomEvent) => {
      console.log('[DEBUG] ReportViewer: Report generation ended:', event.detail.reportPath)
      finishGeneration(event.detail.reportPath)

      // 从文件路径加载报告内容
      if (event.detail.reportPath) {
        console.log('[DEBUG] ReportViewer: Loading report from path:', event.detail.reportPath)
        loadReportFromPath(event.detail.reportPath)
      } else {
        console.log('[DEBUG] ReportViewer: No report path provided')
      }
    }

    console.log('[DEBUG] ReportViewer: Setting up event listeners')
    window.addEventListener('reportGenerationStart', handleReportGenerationStart)
    window.addEventListener('reportGenerationEnd', handleReportGenerationEnd as EventListener)

    return () => {
      console.log('[DEBUG] ReportViewer: Cleaning up event listeners')
      window.removeEventListener('reportGenerationStart', handleReportGenerationStart)
      window.removeEventListener('reportGenerationEnd', handleReportGenerationEnd as EventListener)
    }
  }, [startGeneration, updateReport, finishGeneration])

  // 编辑功能处理函数
  const handleToggleEdit = useCallback(() => {
    if (isEditing) {
      // 退出编辑模式
      if (hasUnsavedChanges) {
        const confirmed = window.confirm('有未保存的更改，确定要退出编辑模式吗？')
        if (!confirmed) return
      }
      setIsEditing(false)
      setEditableContent(originalContent)
      setHasUnsavedChanges(false)
    } else {
      // 进入编辑模式
      const currentContent = reportHtml || reportContent
      setOriginalContent(currentContent)
      setEditableContent(currentContent)
      setIsEditing(true)
      setHasUnsavedChanges(false)
    }
  }, [isEditing, hasUnsavedChanges, originalContent, reportHtml, reportContent])

  const handleContentChange = useCallback((newContent: string) => {
    setEditableContent(newContent)
    setHasUnsavedChanges(newContent !== originalContent)

    // 自动保存
    if (currentTask?.id) {
      handleSaveContent(newContent)
    }
  }, [originalContent, currentTask?.id])

  const handleSaveContent = useCallback(async (content?: string, retryCount = 0) => {
    if (!currentTask?.id) return

    const contentToSave = content || editableContent
    setSaveStatus('saving')

    try {
      const response = await reportService.updateReportContent(currentTask.id, contentToSave)

      if (response.success) {
        setSaveStatus('saved')
        setOriginalContent(contentToSave)
        setHasUnsavedChanges(false)

        // 更新store中的内容
        updateReport(contentToSave)

        console.log('[ReportViewer] Content saved successfully')

        // 3秒后重置保存状态
        setTimeout(() => setSaveStatus('idle'), 3000)
      } else {
        throw new Error(response.message || 'Save failed')
      }
    } catch (error) {
      console.error('[ReportViewer] Failed to save content:', error)

      // 重试机制：最多重试2次
      if (retryCount < 2) {
        console.log(`[ReportViewer] Retrying save (attempt ${retryCount + 1})`)
        setTimeout(() => {
          handleSaveContent(contentToSave, retryCount + 1)
        }, 1000 * (retryCount + 1)) // 递增延迟
      } else {
        setSaveStatus('error')
        setTimeout(() => setSaveStatus('idle'), 5000)
      }
    }
  }, [currentTask?.id, editableContent, updateReport])

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false)
    setEditableContent(originalContent)
    setHasUnsavedChanges(false)
  }, [originalContent])

  const handleExportPDF = useCallback(async () => {
    const content = isEditing ? editableContent : (reportHtml || reportContent)
    if (content) {
      try {
        await reportService.exportToPDF(content, currentTask?.title || 'Report')
      } catch (error) {
        console.error('[ReportViewer] Failed to export PDF:', error)
      }
    }
  }, [isEditing, editableContent, reportHtml, reportContent, currentTask?.title])

  const handleViewBackups = useCallback(async () => {
    if (!currentTask?.id) return

    try {
      const response = await reportService.getReportBackups(currentTask.id)
      if (response.success && response.data) {
        // 这里可以显示备份列表的模态框
        console.log('Backups:', response.data)
        alert(`找到 ${response.data.count} 个备份文件`)
      }
    } catch (error) {
      console.error('[ReportViewer] Failed to get backups:', error)
    }
  }, [currentTask?.id])

  // 加载报告内容的函数
  const loadReportContent = useCallback(async (taskId: string) => {
    try {
      console.log('[DEBUG] ReportViewer: Loading report content for task:', taskId)

      const response = await reportService.getReportContent(taskId)

      if (response.success && response.data) {
        console.log('[DEBUG] ReportViewer: Successfully loaded report content')
        const content = response.data.content
        setReportContent(content)
        setEditableContent(content)
        setOriginalContent(content)

        // 同时更新store中的内容
        updateReport(content)
      } else {
        console.log('[DEBUG] ReportViewer: No report content found for task:', taskId)
        // 如果没有报告内容，清空显示
        setReportContent('')
        setEditableContent('')
        setOriginalContent('')
        updateReport('')
      }
    } catch (error) {
      console.error('[DEBUG] ReportViewer: Failed to load report content:', error)
      // 发生错误时也清空显示
      setReportContent('')
      setEditableContent('')
      setOriginalContent('')
      updateReport('')
    }
  }, [updateReport])

  // 从文件路径加载报告内容
  const loadReportFromPath = useCallback(async (reportPath: string) => {
    try {
      console.log('[DEBUG] ReportViewer: Starting to load report from path:', reportPath)

      // 从报告路径提取任务ID
      const pathParts = reportPath.split('/')
      const taskId = pathParts[pathParts.length - 2] // 倒数第二个部分是任务ID

      console.log('[DEBUG] ReportViewer: Extracted taskId:', taskId)

      // 使用API加载报告内容
      await loadReportContent(taskId)
      console.log('[DEBUG] ReportViewer: Report loaded and updated successfully via API')
    } catch (error) {
      console.error('[DEBUG] ReportViewer: Error loading report:', error)
    }
  }, [loadReportContent])

  // 当任务切换时清除选中的文本并加载新的报告内容
  useEffect(() => {
    setSelectedText('')
    window.getSelection()?.removeAllRanges()

    // 退出编辑模式
    setIsEditing(false)
    setHasUnsavedChanges(false)

    // 根据当前任务加载报告内容
    if (currentTask?.id) {
      loadReportContent(currentTask.id)
    } else {
      setReportContent('')
      setEditableContent('')
      setOriginalContent('')
      updateReport('')
    }
  }, [currentTask?.id, loadReportContent])

  // 同步reportHtml到编辑状态
  useEffect(() => {
    if (reportHtml && !isEditing) {
      setEditableContent(reportHtml)
      setOriginalContent(reportHtml)
    }
  }, [reportHtml, isEditing])

  // 使用合适的内容源
  const displayContent = isEditing ? editableContent : (reportHtml || reportContent)



  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString().trim())
      // 这里可以将选中的文本传递给聊天组件作为上下文
      console.log('Selected text:', selection.toString().trim())
    }
  }

  const clearSelection = () => {
    setSelectedText('')
    window.getSelection()?.removeAllRanges()
  }

  if (!currentTask) {
    return (
      <div className={clsx('flex flex-col items-center justify-center h-full bg-gray-50', className)}>
        <div className="text-center max-w-md">
          <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">
            选择任务查看报告
          </h3>
          <p className="text-gray-600 leading-relaxed">
            请从左侧任务列表中选择一个任务，或创建新任务开始生成报告。
            生成的报告将在此处显示，您可以选择报告中的内容与AI进行讨论。
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={clsx('flex flex-col h-full bg-white', className)}>
      {/* 编辑工具栏 */}
      {displayContent && (
        <EditToolbar
          isEditing={isEditing}
          onToggleEdit={handleToggleEdit}
          onSave={() => handleSaveContent()}
          onCancel={handleCancelEdit}
          onExportPDF={handleExportPDF}
          onViewBackups={handleViewBackups}
          saveStatus={saveStatus}
          hasUnsavedChanges={hasUnsavedChanges}
        />
      )}

      {/* 选中文本提示 */}
      {selectedText && !isEditing && (
        <div className="bg-blue-50 border-b border-blue-200 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span className="text-sm font-medium text-blue-800">
                已选择文本: "{selectedText.substring(0, 50)}{selectedText.length > 50 ? '...' : ''}"
              </span>
            </div>
            <button
              onClick={clearSelection}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              清除选择
            </button>
          </div>
        </div>
      )}

      {/* 报告内容 */}
      <div
        className="flex-1 overflow-auto"
        style={{
          display: 'grid',
          placeItems: 'start center',
          padding: '24px',
          width: '100%'
        }}
      >
        {displayContent ? (
          <div
            style={{
              width: '100%',
              maxWidth: '800px',
              margin: '0 auto'
            }}
          >
            <EditableContent
              content={displayContent}
              editable={isEditing}
              onChange={handleContentChange}
              saveStatus={saveStatus}
              className="w-full"
            />
            {!isEditing && (
              <div
                className="absolute inset-0 pointer-events-none"
                onMouseUp={handleTextSelection}
              />
            )}
          </div>
        ) : isGenerating ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 animate-pulse">
              <svg className="w-8 h-8 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              正在生成报告
            </h3>
            <p className="text-gray-600 max-w-md">
              AI正在为您生成详细的数据分析报告，请稍候...
            </p>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              报告生成中
            </h3>
            <p className="text-gray-600 max-w-md">
              当前任务还没有生成报告。请在右侧聊天区域描述您的需求，AI将为您生成详细的数据分析报告。
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ReportViewer
