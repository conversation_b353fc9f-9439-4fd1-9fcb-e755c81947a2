import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import ReportViewer from './index'
import { useTaskStore, useReportStore } from '../../../stores'
import { reportService } from '../../../services/api/reportService'

// Mock the stores
vi.mock('../../../stores', () => ({
  useTaskStore: vi.fn(),
  useReportStore: vi.fn()
}))

// Mock the report service
vi.mock('../../../services/api/reportService', () => ({
  reportService: {
    getReportContent: vi.fn(),
    updateReportContent: vi.fn(),
    getReportBackups: vi.fn(),
    exportToPDF: vi.fn()
  }
}))

// Mock React Router
vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/' })
}))

describe('ReportViewer API Integration', () => {
  const mockTask = {
    id: 'test-task-id',
    title: 'Test Task',
    report_file_path: '/reports/test-task-id'
  }

  const mockReportContent = {
    content: '<div><h1>Test Report</h1><p>This is a test report content.</p></div>',
    file_path: '/reports/test-task-id/report.html',
    last_modified: '2024-01-01T00:00:00Z'
  }

  const mockTaskStore = {
    currentTask: mockTask
  }

  const mockReportStore = {
    reportHtml: '',
    isGenerating: false,
    updateReport: vi.fn(),
    startGeneration: vi.fn(),
    finishGeneration: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup store mocks
    vi.mocked(useTaskStore).mockReturnValue(mockTaskStore as any)
    vi.mocked(useReportStore).mockReturnValue(mockReportStore as any)
  })

  it('should load report content via API when task is selected', async () => {
    // Mock successful API response
    vi.mocked(reportService.getReportContent).mockResolvedValue({
      success: true,
      data: mockReportContent
    } as any)

    render(<ReportViewer />)

    // Wait for the API call to be made
    await waitFor(() => {
      expect(reportService.getReportContent).toHaveBeenCalledWith('test-task-id')
    })

    // Verify that updateReport was called with the content
    expect(mockReportStore.updateReport).toHaveBeenCalledWith(mockReportContent.content)
  })

  it('should handle API error gracefully', async () => {
    // Mock API error
    vi.mocked(reportService.getReportContent).mockRejectedValue(new Error('API Error'))

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(<ReportViewer />)

    // Wait for the API call to be made
    await waitFor(() => {
      expect(reportService.getReportContent).toHaveBeenCalledWith('test-task-id')
    })

    // Verify error was logged
    expect(consoleSpy).toHaveBeenCalledWith(
      '[DEBUG] ReportViewer: Failed to load report content:',
      expect.any(Error)
    )

    // Verify that updateReport was called with empty content
    expect(mockReportStore.updateReport).toHaveBeenCalledWith('')

    consoleSpy.mockRestore()
  })

  it('should handle empty API response', async () => {
    // Mock empty API response
    vi.mocked(reportService.getReportContent).mockResolvedValue({
      success: false,
      data: null
    } as any)

    render(<ReportViewer />)

    // Wait for the API call to be made
    await waitFor(() => {
      expect(reportService.getReportContent).toHaveBeenCalledWith('test-task-id')
    })

    // Verify that updateReport was called with empty content
    expect(mockReportStore.updateReport).toHaveBeenCalledWith('')
  })

  it('should not make API call when no task is selected', () => {
    // Mock no current task
    vi.mocked(useTaskStore).mockReturnValue({
      currentTask: null
    } as any)

    render(<ReportViewer />)

    // Verify no API call was made
    expect(reportService.getReportContent).not.toHaveBeenCalled()
  })

  it('should display loading state when generating report', () => {
    // Mock generating state
    vi.mocked(useReportStore).mockReturnValue({
      ...mockReportStore,
      isGenerating: true
    } as any)

    render(<ReportViewer />)

    // Check for loading indicators
    expect(screen.getByText('正在生成报告')).toBeInTheDocument()
    expect(screen.getByText('AI正在为您生成详细的数据分析报告，请稍候...')).toBeInTheDocument()
  })

  it('should display report content when loaded', async () => {
    // Mock successful API response
    vi.mocked(reportService.getReportContent).mockResolvedValue({
      success: true,
      data: mockReportContent
    } as any)

    render(<ReportViewer />)

    // Wait for content to be loaded and displayed
    await waitFor(() => {
      expect(screen.getByText('Test Report')).toBeInTheDocument()
    })

    expect(screen.getByText('This is a test report content.')).toBeInTheDocument()
  })
})
