import React from 'react'
import clsx from 'clsx'

export interface EditToolbarProps {
  /** 是否处于编辑模式 */
  isEditing: boolean
  /** 切换编辑模式 */
  onToggleEdit: () => void
  /** 保存内容 */
  onSave?: () => void
  /** 取消编辑 */
  onCancel?: () => void
  /** 导出PDF */
  onExportPDF?: () => void
  /** 查看备份 */
  onViewBackups?: () => void
  /** 保存状态 */
  saveStatus?: 'idle' | 'saving' | 'saved' | 'error'
  /** 是否有未保存的更改 */
  hasUnsavedChanges?: boolean
  /** 自定义样式类名 */
  className?: string
}

const EditToolbar: React.FC<EditToolbarProps> = ({
  isEditing,
  onToggleEdit,
  onSave,
  onCancel,
  onExportPDF,
  onViewBackups,
  saveStatus = 'idle',
  hasUnsavedChanges = false,
  className = ''
}) => {
  const renderEditButton = () => {
    if (isEditing) {
      return (
        <div className="flex items-center space-x-2">
          <button
            onClick={onSave}
            disabled={saveStatus === 'saving'}
            className={clsx(
              'flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
              saveStatus === 'saving'
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-green-500 text-white hover:bg-green-600'
            )}
          >
            {saveStatus === 'saving' ? (
              <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            )}
            <span>{saveStatus === 'saving' ? '保存中...' : '保存'}</span>
          </button>
          
          <button
            onClick={onCancel}
            className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span>取消</span>
          </button>
        </div>
      )
    }

    return (
      <button
        onClick={onToggleEdit}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
        <span>编辑</span>
      </button>
    )
  }

  const renderUtilityButtons = () => {
    if (isEditing) return null

    return (
      <div className="flex items-center space-x-2">
        {onExportPDF && (
          <button
            onClick={onExportPDF}
            className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-colors"
            title="导出PDF"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>导出PDF</span>
          </button>
        )}
        
        {onViewBackups && (
          <button
            onClick={onViewBackups}
            className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-colors"
            title="查看备份"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>备份</span>
          </button>
        )}
      </div>
    )
  }

  const renderStatusIndicator = () => {
    if (!isEditing || !hasUnsavedChanges) return null

    return (
      <div className="flex items-center space-x-2 text-sm text-amber-600">
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>有未保存的更改</span>
      </div>
    )
  }

  return (
    <div className={clsx(
      'flex items-center justify-between p-3 bg-white border-b border-gray-200',
      className
    )}>
      <div className="flex items-center space-x-4">
        {renderEditButton()}
        {renderStatusIndicator()}
      </div>
      
      {renderUtilityButtons()}
    </div>
  )
}

export default EditToolbar
